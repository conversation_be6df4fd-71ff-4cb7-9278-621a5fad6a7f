#!/usr/bin/env python3
"""
Level 2 Agno Example - Your Original Code Integrated
====================================================

This is your original Level 2 Agno code integrated into the project structure.
It demonstrates the exact pattern you provided, but organized within our project.

This example uses:
- Claude Sonnet 4 model (requires Anthropic API key)
- OpenAI embeddings (requires OpenAI API key)
- URL-based knowledge loading from Agno docs
- Hybrid search with LanceDB
- SQLite storage for sessions

To run this example:
1. Set your API keys:
   export ANTHROPIC_API_KEY=your_key_here
   export OPENAI_API_KEY=your_key_here

2. Run the script:
   python examples/level2_example.py
"""

import os
import sys

# Add the src directory to the path so we can import modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def check_requirements():
    """Check if required packages and API keys are available"""
    
    # Check API keys
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not anthropic_key:
        print("❌ Missing ANTHROPIC_API_KEY environment variable")
        print("Please set it with: export ANTHROPIC_API_KEY=your_key_here")
        return False
    
    if not openai_key:
        print("❌ Missing OPENAI_API_KEY environment variable")
        print("Please set it with: export OPENAI_API_KEY=your_key_here")
        return False
    
    # Check required packages
    try:
        from agno.agent import Agent
        from agno.embedder.openai import OpenAIEmbedder
        from agno.knowledge.url import UrlKnowledge
        from agno.models.anthropic import Claude
        from agno.storage.sqlite import SqliteStorage
        from agno.vectordb.lancedb import LanceDb, SearchType
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please install with: pip install -r config/requirements.txt")
        print("And uncomment the cloud dependencies in requirements.txt")
        return False
    
    return True

def create_level2_agent():
    """
    Create Level 2 Agno agent - Your original code pattern
    """
    
    # Import here after checking requirements
    from agno.agent import Agent
    from agno.embedder.openai import OpenAIEmbedder
    from agno.knowledge.url import UrlKnowledge
    from agno.models.anthropic import Claude
    from agno.storage.sqlite import SqliteStorage
    from agno.vectordb.lancedb import LanceDb, SearchType
    
    print("🚀 Creating Level 2 Agno Agent (Your Original Pattern)...")
    
    # Load Agno documentation in a knowledge base
    # You can also use `https://docs.agno.com/llms-full.txt` for the full documentation
    knowledge = UrlKnowledge(
        urls=["https://docs.agno.com/introduction.md"],
        vector_db=LanceDb(
            uri="tmp/lancedb",
            table_name="agno_docs",
            search_type=SearchType.hybrid,
            # Use OpenAI for embeddings
            embedder=OpenAIEmbedder(id="text-embedding-3-small", dimensions=1536),
        ),
    )

    # Store agent sessions in a SQLite database
    storage = SqliteStorage(table_name="agent_sessions", db_file="tmp/agent.db")

    agent = Agent(
        name="Agno Assist",
        model=Claude(id="claude-sonnet-4-20250514"),
        instructions=[
            "Search your knowledge before answering the question.",
            "Only include the output in your response. No other text.",
        ],
        knowledge=knowledge,
        storage=storage,
        add_datetime_to_instructions=True,
        # Add the chat history to the messages
        add_history_to_messages=True,
        # Number of history runs
        num_history_runs=3,
        markdown=True,
    )
    
    return agent, knowledge

def main():
    """Main function - Your original code pattern"""
    
    print("🤖 Level 2 Agno Example - Your Original Code")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return
    
    # Create tmp directory if needed
    os.makedirs("tmp", exist_ok=True)
    
    # Create agent
    agent, knowledge = create_level2_agent()
    
    # Load the knowledge base, comment out after first run
    # Set recreate to True to recreate the knowledge base if needed
    print("📚 Loading knowledge base...")
    try:
        knowledge.load(recreate=False)
        print("✅ Knowledge base loaded successfully!")
    except Exception as e:
        print(f"⚠️  Warning: Could not load knowledge base: {e}")
        print("The agent will still work without the knowledge base.")
    
    # Your original question
    print("\n🤖 Running your original example...")
    print("Question: What is Agno?")
    print("-" * 40)
    agent.print_response("What is Agno?", stream=True)
    
    print("\n" + "=" * 50)
    print("🎉 Example completed!")
    print("\nThis demonstrates your original Level 2 Agno pattern.")
    print("You can now:")
    print("1. Modify the knowledge URLs")
    print("2. Change the model or embeddings")
    print("3. Customize the agent instructions")
    print("4. Add more sophisticated features")

def interactive_mode():
    """Interactive chat mode with your original agent"""
    
    print("🤖 Level 2 Agno Interactive Mode")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return
    
    # Create tmp directory if needed
    os.makedirs("tmp", exist_ok=True)
    
    # Create agent
    agent, knowledge = create_level2_agent()
    
    # Load knowledge base
    print("📚 Loading knowledge base...")
    try:
        knowledge.load(recreate=False)
        print("✅ Knowledge base loaded successfully!")
    except Exception as e:
        print(f"⚠️  Warning: Could not load knowledge base: {e}")
    
    print("\n🤖 Agent ready! Ask questions about Agno or anything else.")
    print("Type 'quit', 'exit', or 'bye' to stop.")
    print("=" * 50)
    
    # Interactive chat loop
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                print("\n👋 Goodbye! Thanks for using the Level 2 Agno agent.")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 Assistant:")
            print("-" * 40)
            
            # Get response from agent
            agent.print_response(user_input, stream=True)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Thanks for using the Level 2 Agno agent.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'quit' to exit.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_mode()
    else:
        main()
