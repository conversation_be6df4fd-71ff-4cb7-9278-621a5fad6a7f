# Agno Framework with Ollama Support
agno>=1.7.6

# Ollama Python client
ollama>=0.5.1

# Local embeddings (no API key required)
sentence-transformers>=2.2.0

# Vector database with hybrid search support
lancedb>=0.5.0

# Additional dependencies for local processing
torch>=2.0.0
transformers>=4.30.0

# Web scraping and URL knowledge support
requests>=2.28.0
beautifulsoup4>=4.12.0
urllib3>=1.26.0

# Optional: For better performance
numpy>=1.24.0
pandas>=2.0.0

# Optional: Cloud model support (requires API keys)
# Uncomment these if you want to use cloud models
# anthropic>=0.25.0
# openai>=1.0.0
