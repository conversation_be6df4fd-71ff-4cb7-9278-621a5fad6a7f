# Agno Level 2 Agents Collection

This project demonstrates multiple **Level 2 Agno Agents** with different capabilities:

## 🤖 Available Agents

### 1. 🏠 Simple Local Agent (`simple_ollama_agent.py`)
- 🦙 **Ollama** with **Llama 3.2** model (runs locally, no API keys needed)
- 🧠 **Local embeddings** using Sentence Transformers
- 💾 **SQLite storage** for session management
- 📚 **Basic knowledge base** with LanceDB vector database

### 2. 🚀 Advanced Local Agent (`advanced_ollama_agent.py`)
- 🦙 **Ollama** with **Llama 3.2** model (100% local)
- 🌐 **URL-based knowledge loading** (loads from web sources)
- 🔍 **Hybrid search** (vector + text search)
- 💾 **Advanced session management**
- 📚 **Configurable knowledge sources**

### 3. ☁️ Cloud Agent (`cloud_agent.py`)
- 🧠 **Claude Sonnet 4** model (highest quality)
- 🌐 **OpenAI embeddings** for superior search
- 🔍 **Hybrid search capabilities**
- 💾 **Advanced session management**
- 📚 **URL-based knowledge loading**

### 4. 🎯 Agent Launcher (`agent_launcher.py`)
- 🔄 **Easy switching** between agent types
- ✅ **Availability checking** for models and API keys
- 🧪 **Demo modes** for testing
- 📋 **Interactive menu** for selection

## 🌟 Features Comparison

| Feature | Simple Local | Advanced Local | Cloud |
|---------|-------------|----------------|-------|
| **Cost** | 100% Free | 100% Free | Requires API keys |
| **Privacy** | 100% Local | 100% Local | Cloud-based |
| **Knowledge Loading** | Static text | URLs + Web | URLs + Web |
| **Search Type** | Vector only | Hybrid (vector + text) | Hybrid (vector + text) |
| **Model Quality** | Good | Good | Excellent |
| **Setup Complexity** | Simple | Medium | Medium |
| **Internet Required** | No (after setup) | Yes (for knowledge) | Yes |

## 🚀 Common Features

- 🧠 **Memory & Knowledge**: Level 2 agent with persistent storage and knowledge base
- 💬 **Interactive Chat**: Real-time conversation with AI
- 📚 **Knowledge Search**: Agent searches knowledge base before answering
- 🔄 **Session History**: Remembers conversation context
- 📝 **Markdown Support**: Rich text formatting in responses

## Prerequisites

1. **Ollama installed** - Visit [ollama.com](https://ollama.com) to install
2. **Python 3.8+** with pip
3. **At least 4GB RAM** for running Llama 3.2

## Quick Setup

### Option 1: Automated Setup (Recommended)

```bash
# Run the setup script
./setup_ollama_agent.sh
```

### Option 2: Manual Setup

1. **Install Ollama** (if not already installed):
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ```

2. **Pull Llama 3.2 model**:
   ```bash
   ollama pull llama3.2
   ```

3. **Install Python dependencies**:
   ```bash
   pip install -r requirements_ollama.txt
   ```

4. **Create directories**:
   ```bash
   mkdir -p tmp
   ```

## 🎯 Quick Start

### Option 1: Use the Agent Launcher (Recommended)

```bash
# Interactive menu to choose agent type
python src/agent_launcher.py

# Or directly specify agent type
python src/agent_launcher.py local      # Simple local agent
python src/agent_launcher.py advanced   # Advanced local agent
python src/agent_launcher.py cloud      # Cloud agent (requires API keys)

# Run demos
python src/agent_launcher.py demo local
python src/agent_launcher.py demo advanced
python src/agent_launcher.py demo cloud
```

### Option 2: Run Agents Directly

#### Simple Local Agent
```bash
python src/simple_ollama_agent.py        # Interactive chat
python src/simple_ollama_agent.py demo   # Demo mode
```

#### Advanced Local Agent
```bash
python src/advanced_ollama_agent.py      # Interactive chat
python src/advanced_ollama_agent.py demo # Demo mode
```

#### Cloud Agent (requires API keys)
```bash
# Set API keys first
export ANTHROPIC_API_KEY=your_anthropic_key
export OPENAI_API_KEY=your_openai_key

python src/cloud_agent.py               # Interactive chat
python src/cloud_agent.py demo          # Demo mode
```

## Example Conversation

```
🤖 Agent ready! You can now chat with your local Llama 3.2 model.
============================================================

👤 You: What is Python programming?

🤖 Assistant:
----------------------------------------
Python is a high-level, interpreted programming language known for its 
simplicity and readability. Key features include dynamic typing, automatic 
memory management, and extensive standard library.

Python is widely used for:
- Web development
- Data science and machine learning
- Automation and scripting
- Desktop applications

👤 You: Write a simple function to add two numbers

🤖 Assistant:
----------------------------------------
Here's a simple Python function to add two numbers:

```python
def add_numbers(a, b):
    """
    Add two numbers and return the result.
    
    Args:
        a: First number
        b: Second number
    
    Returns:
        The sum of a and b
    """
    return a + b

# Example usage
result = add_numbers(5, 3)
print(f"5 + 3 = {result}")  # Output: 5 + 3 = 8
```
```

## Project Structure

```
.
├── ollama_agent.py           # Main agent script
├── requirements_ollama.txt   # Python dependencies
├── setup_ollama_agent.sh     # Automated setup script
├── README_Ollama_Agent.md    # This file
└── tmp/                      # Created automatically
    ├── lancedb_ollama/       # Vector database storage
    └── ollama_agent.db       # SQLite session storage
```

## Customization

### Using Different Models

You can easily switch to other Ollama models by modifying the `ollama_agent.py` file:

```python
# In the create_agent() function, change:
model=Ollama(id="llama3.2")

# To any other model, for example:
model=Ollama(id="llama3.2:1b")    # Smaller, faster version
model=Ollama(id="qwen2.5")        # Good for tool use
model=Ollama(id="deepseek-r1")    # Strong reasoning
model=Ollama(id="phi4")           # Small but powerful
```

### Adding Your Own Knowledge

Modify the `create_knowledge_base()` function in `ollama_agent.py` to add your own knowledge:

```python
knowledge_content = """
Your custom knowledge here...
Add information about your domain, company, or specific topics.
"""
```

### Adjusting Agent Behavior

Customize the agent's instructions in the `create_agent()` function:

```python
instructions=[
    "Your custom instructions here",
    "Define the agent's personality and behavior",
    "Add specific guidelines for responses",
]
```

## Troubleshooting

### Ollama Not Found
```bash
# Install Ollama
curl -fsSL https://ollama.com/install.sh | sh

# Or visit https://ollama.com for platform-specific instructions
```

### Model Not Downloaded
```bash
# Pull the model manually
ollama pull llama3.2

# Check available models
ollama list
```

### Memory Issues
If you encounter memory issues, try a smaller model:
```bash
ollama pull llama3.2:1b  # 1B parameter version (smaller)
```

### Dependencies Issues
```bash
# Reinstall dependencies
pip install --upgrade -r requirements_ollama.txt

# Or install individually
pip install agno ollama sentence-transformers lancedb
```

## Performance Tips

1. **Use smaller models** for faster responses: `llama3.2:1b` or `phi4`
2. **Increase RAM** allocation if possible
3. **Use SSD storage** for better vector database performance
4. **Close other applications** to free up memory

## What's Next?

This is a **Level 2 Agent** with knowledge and storage. You can extend it to:

- **Level 3**: Add reasoning capabilities with `ReasoningTools`
- **Level 4**: Create agent teams for complex tasks
- **Level 5**: Build agentic workflows with state management

Check out the [Agno documentation](https://docs.agno.com) for more advanced features!

## Contributing

Feel free to improve this example:
- Add more sophisticated knowledge bases
- Implement additional tools
- Create specialized agents for different domains
- Optimize performance

## License

This project is open source and available under the MIT License.
