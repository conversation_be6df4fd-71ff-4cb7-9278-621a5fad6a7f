# Level 2 Agno Setup Guide

This guide helps you set up and use the Level 2 Agno agents in this project, including your original code pattern.

## 🎯 Quick Decision Guide

**Choose your agent based on your needs:**

| Need | Recommended Agent | Setup Time |
|------|------------------|------------|
| **Learning Agno** | Simple Local Agent | 5 minutes |
| **Free + Advanced Features** | Advanced Local Agent | 10 minutes |
| **Highest Quality** | Cloud Agent | 5 minutes (with API keys) |
| **Your Original Code** | Level 2 Example | 5 minutes (with API keys) |

## 🏠 Local Agents Setup (Free)

### Prerequisites
1. **Ollama installed** - Visit [ollama.com](https://ollama.com)
2. **Python 3.8+** with pip
3. **4GB+ RAM** for Llama 3.2

### Step 1: Install Ollama
```bash
# Linux/Mac
curl -fsSL https://ollama.com/install.sh | sh

# Windows: Download from https://ollama.com
```

### Step 2: Pull Llama 3.2 Model
```bash
ollama pull llama3.2

# Optional: Smaller models for lower-end hardware
ollama pull llama3.2:1b  # 1B parameters (faster)
ollama pull llama3.2:3b  # 3B parameters (balanced)
```

### Step 3: Install Dependencies
```bash
cd ollama_agent
pip install -r config/requirements.txt
```

### Step 4: Run Local Agent
```bash
# Use the launcher (recommended)
python src/agent_launcher.py

# Or run directly
python src/advanced_ollama_agent.py
```

## ☁️ Cloud Agent Setup (API Keys Required)

### Prerequisites
1. **Anthropic API Key** - Get from [console.anthropic.com](https://console.anthropic.com)
2. **OpenAI API Key** - Get from [platform.openai.com](https://platform.openai.com)

### Step 1: Get API Keys
1. **Anthropic API Key**:
   - Visit [console.anthropic.com](https://console.anthropic.com)
   - Create account and get API key
   - Note: Requires payment method

2. **OpenAI API Key**:
   - Visit [platform.openai.com](https://platform.openai.com)
   - Create account and get API key
   - Note: Requires payment method

### Step 2: Set Environment Variables
```bash
# Linux/Mac
export ANTHROPIC_API_KEY=your_anthropic_key_here
export OPENAI_API_KEY=your_openai_key_here

# Windows
set ANTHROPIC_API_KEY=your_anthropic_key_here
set OPENAI_API_KEY=your_openai_key_here

# Or add to your shell profile (.bashrc, .zshrc, etc.)
echo 'export ANTHROPIC_API_KEY=your_key_here' >> ~/.bashrc
echo 'export OPENAI_API_KEY=your_key_here' >> ~/.bashrc
```

### Step 3: Install Cloud Dependencies
```bash
cd ollama_agent
pip install -r config/requirements.txt

# Uncomment and install cloud dependencies
pip install anthropic>=0.25.0 openai>=1.0.0
```

### Step 4: Run Cloud Agent
```bash
# Use the launcher
python src/agent_launcher.py

# Or run directly
python src/cloud_agent.py
```

## 🧪 Your Original Level 2 Code

### Setup
```bash
# Set API keys (same as cloud agent)
export ANTHROPIC_API_KEY=your_key_here
export OPENAI_API_KEY=your_key_here

# Install dependencies
pip install anthropic openai

# Run your original pattern
python examples/level2_example.py

# Or interactive mode
python examples/level2_example.py interactive
```

## 🚀 Usage Examples

### 1. Agent Launcher (Recommended)
```bash
# Interactive menu
python src/agent_launcher.py

# Direct selection
python src/agent_launcher.py local      # Local agent
python src/agent_launcher.py advanced   # Advanced local
python src/agent_launcher.py cloud      # Cloud agent

# Demo modes
python src/agent_launcher.py demo local
```

### 2. Individual Agents
```bash
# Simple local agent
python src/simple_ollama_agent.py
python src/simple_ollama_agent.py demo

# Advanced local agent
python src/advanced_ollama_agent.py
python src/advanced_ollama_agent.py demo

# Cloud agent
python src/cloud_agent.py
python src/cloud_agent.py demo
```

## 🔧 Configuration

### Local Agent Configuration
Edit `src/advanced_ollama_agent.py`:
```python
config = {
    "model_name": "llama3.2",  # or "llama3.2:1b", "llama3.2:3b"
    "knowledge_urls": [
        "https://docs.agno.com/introduction.md",
        "https://your-custom-docs.com/api.md",  # Add your URLs
    ],
    "use_hybrid_search": True,  # Vector + text search
    "embedder_model": "all-MiniLM-L6-v2",  # Local embeddings
}
```

### Cloud Agent Configuration
Edit `src/cloud_agent.py`:
```python
config = {
    "model_name": "claude-sonnet-4-20250514",
    "knowledge_urls": [
        "https://docs.agno.com/introduction.md",
        # "https://docs.agno.com/llms-full.txt",  # Full docs
    ],
    "embedder_model": "text-embedding-3-small",
    "embedder_dimensions": 1536,
}
```

## 🛠️ Troubleshooting

### Ollama Issues
```bash
# Check if Ollama is running
ollama list

# Start Ollama service (if needed)
ollama serve

# Pull missing models
ollama pull llama3.2
```

### API Key Issues
```bash
# Check if keys are set
echo $ANTHROPIC_API_KEY
echo $OPENAI_API_KEY

# Test API keys
python -c "import anthropic; print('Anthropic OK')"
python -c "import openai; print('OpenAI OK')"
```

### Memory Issues
```bash
# Use smaller models
ollama pull llama3.2:1b  # 1B parameters

# Or adjust model in code
model=Ollama(id="llama3.2:1b")
```

### Dependencies Issues
```bash
# Reinstall dependencies
pip install --upgrade -r config/requirements.txt

# Install specific packages
pip install agno>=1.7.6 ollama>=0.5.1
```

## 📊 Performance Comparison

| Agent Type | Response Quality | Speed | Cost | Privacy |
|------------|------------------|-------|------|---------|
| Simple Local | Good | Fast | Free | 100% Private |
| Advanced Local | Good | Medium | Free | 100% Private |
| Cloud | Excellent | Fast | Pay-per-use | Cloud-based |

## 🎯 Next Steps

1. **Start with Simple Local** to learn Agno basics
2. **Upgrade to Advanced Local** for better features
3. **Try Cloud Agent** for highest quality
4. **Customize** agents for your specific use case
5. **Build Level 3+** agents with reasoning and tools

## 📚 Additional Resources

- [Agno Documentation](https://docs.agno.com)
- [Ollama Models](https://ollama.com/library)
- [Anthropic API Docs](https://docs.anthropic.com)
- [OpenAI API Docs](https://platform.openai.com/docs)

## 🤝 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review the agent-specific README files
3. Test with the demo modes
4. Check the examples directory
