#!/usr/bin/env python3
"""
Advanced Agno Level 2 Agent with Ollama
=======================================

This script demonstrates an advanced Agno Level 2 Agent that combines:
- Local Ollama models (free, no API keys)
- URL-based knowledge loading
- Hybrid search capabilities
- Advanced session management
- Configurable model switching

Features:
- 🏠 100% local execution with Ollama
- 🌐 Load knowledge from URLs (like Agno docs)
- 🔍 Hybrid vector search
- 💾 Persistent conversation memory
- 🔄 Easy model switching
- 📚 Local embeddings with Sentence Transformers

Requirements:
- Ollama installed and running
- llama3.2 model pulled via ollama
- agno package with dependencies installed
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.embedder.sentence_transformer import SentenceTransformerEmbedder
from agno.knowledge.url import UrlKnowledge
from agno.storage.sqlite import SqliteStorage
from agno.vectordb.lancedb import LanceDb, SearchType
import os
import sys
from typing import Optional, List

class AdvancedOllamaAgent:
    """Advanced Ollama Agent with Level 2 Agno capabilities"""
    
    def __init__(
        self,
        model_name: str = "llama3.2",
        knowledge_urls: Optional[List[str]] = None,
        use_hybrid_search: bool = True,
        embedder_model: str = "all-MiniLM-L6-v2",
        storage_db: str = "tmp/advanced_ollama_agent.db",
        vector_db_uri: str = "tmp/advanced_lancedb",
    ):
        """
        Initialize the advanced Ollama agent
        
        Args:
            model_name: Ollama model to use (e.g., "llama3.2", "llama3.2:1b")
            knowledge_urls: List of URLs to load as knowledge base
            use_hybrid_search: Whether to use hybrid search (vector + text)
            embedder_model: Sentence transformer model for embeddings
            storage_db: SQLite database file for session storage
            vector_db_uri: LanceDB URI for vector storage
        """
        self.model_name = model_name
        self.knowledge_urls = knowledge_urls or [
            "https://docs.agno.com/introduction.md"
        ]
        self.use_hybrid_search = use_hybrid_search
        self.embedder_model = embedder_model
        self.storage_db = storage_db
        self.vector_db_uri = vector_db_uri
        self.agent = None
        
    def _create_knowledge_base(self):
        """Create knowledge base from URLs with local embeddings"""
        
        print(f"📚 Creating knowledge base from {len(self.knowledge_urls)} URL(s)...")
        
        # Create embedder with local sentence transformer
        embedder = SentenceTransformerEmbedder(
            model=self.embedder_model,
            dimensions=384  # all-MiniLM-L6-v2 produces 384-dim embeddings
        )
        
        # Create vector database with hybrid search if enabled
        search_type = SearchType.hybrid if self.use_hybrid_search else SearchType.vector
        
        vector_db = LanceDb(
            uri=self.vector_db_uri,
            table_name="advanced_agno_docs",
            search_type=search_type,
            embedder=embedder,
        )
        
        # Create knowledge base from URLs
        knowledge = UrlKnowledge(
            urls=self.knowledge_urls,
            vector_db=vector_db,
        )
        
        return knowledge
    
    def _create_storage(self):
        """Create SQLite storage for session management"""
        return SqliteStorage(
            table_name="advanced_agent_sessions", 
            db_file=self.storage_db
        )
    
    def _create_agent(self):
        """Create the Agno agent with all components"""
        
        # Create knowledge base
        knowledge = self._create_knowledge_base()
        
        # Create storage
        storage = self._create_storage()
        
        # Create agent
        agent = Agent(
            name="Advanced Ollama Assistant",
            model=Ollama(id=self.model_name),
            instructions=[
                "You are an advanced AI assistant powered by Ollama running locally.",
                "Search your knowledge base before answering questions when relevant.",
                "You have access to Agno framework documentation and other knowledge sources.",
                "Be helpful, accurate, and concise in your responses.",
                "If you find relevant information in your knowledge base, reference it.",
                "If you don't know something, say so honestly.",
            ],
            knowledge=knowledge,
            storage=storage,
            # Advanced session management
            add_datetime_to_instructions=True,
            add_history_to_messages=True,
            num_history_runs=3,
            # Better formatting and debugging
            markdown=True,
            show_tool_calls=True,
        )
        
        return agent
    
    def initialize(self, recreate_knowledge: bool = False):
        """Initialize the agent and load knowledge base"""
        
        print("🚀 Initializing Advanced Agno Agent with Ollama...")
        print("=" * 60)
        
        # Check if ollama is running and model is available
        if not self._check_ollama():
            return False
        
        # Create tmp directory if needed
        os.makedirs("tmp", exist_ok=True)
        
        # Create the agent
        print("🤖 Creating agent...")
        self.agent = self._create_agent()
        
        # Load knowledge base
        print("📚 Loading knowledge base...")
        try:
            self.agent.knowledge.load(recreate=recreate_knowledge)
            print("✅ Knowledge base loaded successfully!")
            return True
        except Exception as e:
            print(f"⚠️  Warning: Could not load knowledge base: {e}")
            print("The agent will still work without the knowledge base.")
            return True
    
    def _check_ollama(self):
        """Check if Ollama is running and model is available"""
        try:
            import subprocess
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
            if self.model_name not in result.stdout:
                print(f"⚠️  Warning: {self.model_name} model not found!")
                print(f"Please run: ollama pull {self.model_name}")
                print("Available models:")
                print(result.stdout)
                return False
            return True
        except FileNotFoundError:
            print("❌ Error: Ollama not found. Please install Ollama first.")
            print("Visit: https://ollama.com")
            return False
    
    def chat(self):
        """Start interactive chat session"""
        
        if not self.agent:
            print("❌ Agent not initialized. Call initialize() first.")
            return
        
        print("\n🤖 Advanced Agent ready! You can now chat with your local AI assistant.")
        print("Features: Knowledge base search, session memory, hybrid search")
        print("Type 'quit', 'exit', or 'bye' to stop the conversation.")
        print("=" * 60)
        
        # Interactive chat loop
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                    print("\n👋 Goodbye! Thanks for using the Advanced Ollama agent.")
                    break
                
                if not user_input:
                    continue
                
                print("\n🤖 Assistant:")
                print("-" * 40)
                
                # Get response from agent
                self.agent.print_response(user_input, stream=True)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using the Advanced Ollama agent.")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("Please try again or type 'quit' to exit.")
    
    def ask(self, question: str, stream: bool = True):
        """Ask a single question to the agent"""
        
        if not self.agent:
            print("❌ Agent not initialized. Call initialize() first.")
            return
        
        print(f"\n📝 Question: {question}")
        print("-" * 50)
        self.agent.print_response(question, stream=stream)


def main():
    """Main function to run the advanced Ollama agent"""
    
    # Configuration - you can modify these
    config = {
        "model_name": "llama3.2",  # or "llama3.2:1b", "llama3.2:3b"
        "knowledge_urls": [
            "https://docs.agno.com/introduction.md",
            # Add more URLs as needed
        ],
        "use_hybrid_search": True,
        "embedder_model": "all-MiniLM-L6-v2",  # Fast, good quality
    }
    
    # Create agent
    agent = AdvancedOllamaAgent(**config)
    
    # Initialize (set recreate_knowledge=True to refresh knowledge base)
    if agent.initialize(recreate_knowledge=False):
        # Start chat
        agent.chat()


def demo():
    """Run demo questions to test the agent"""
    
    print("🧪 Running Advanced Agent Demo...")
    
    # Create agent with demo configuration
    agent = AdvancedOllamaAgent(
        model_name="llama3.2",
        knowledge_urls=["https://docs.agno.com/introduction.md"],
        use_hybrid_search=True,
    )
    
    # Initialize
    if not agent.initialize(recreate_knowledge=False):
        return
    
    # Demo questions
    demo_questions = [
        "What is Agno?",
        "How do I create an agent with knowledge base?",
        "What are the different levels of Agno agents?",
        "How does vector search work in Agno?",
        "Write a simple Python function to create an Ollama agent",
    ]
    
    for i, question in enumerate(demo_questions, 1):
        print(f"\n📝 Demo Question {i}: {question}")
        print("=" * 60)
        agent.ask(question, stream=True)
        print("\n" + "=" * 60)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo()
    else:
        main()
