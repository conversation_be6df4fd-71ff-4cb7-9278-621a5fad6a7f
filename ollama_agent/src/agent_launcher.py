#!/usr/bin/env python3
"""
Agno Agent Launcher - Choose Your Agent Type
============================================

This launcher helps you choose between different types of Agno agents:

1. 🏠 Local Agent (Ollama) - Free, runs locally, no API keys needed
2. ☁️  Cloud Agent (Claude) - High quality, requires API keys
3. 🚀 Advanced Local Agent - Local with advanced features

Usage:
    python agent_launcher.py                    # Interactive menu
    python agent_launcher.py local              # Run local agent
    python agent_launcher.py cloud              # Run cloud agent
    python agent_launcher.py advanced           # Run advanced local agent
    python agent_launcher.py demo local         # Run local demo
    python agent_launcher.py demo cloud         # Run cloud demo
"""

import sys
import os
from typing import Optional

def print_banner():
    """Print the application banner"""
    print("🤖 Agno Agent Launcher")
    print("=" * 50)
    print("Choose your AI agent type:")
    print()

def print_agent_options():
    """Print available agent options"""
    print("Available Agents:")
    print()
    print("1. 🏠 Local Agent (Simple)")
    print("   - Uses Ollama with Llama 3.2")
    print("   - 100% free, no API keys needed")
    print("   - Basic knowledge base")
    print("   - Good for learning and basic tasks")
    print()
    print("2. 🚀 Advanced Local Agent")
    print("   - Uses Ollama with Llama 3.2")
    print("   - 100% free, no API keys needed")
    print("   - URL-based knowledge loading")
    print("   - Hybrid search capabilities")
    print("   - Advanced session management")
    print()
    print("3. ☁️  Cloud Agent (Claude)")
    print("   - Uses Claude Sonnet 4")
    print("   - Requires API keys (Anthropic + OpenAI)")
    print("   - Highest quality responses")
    print("   - Advanced reasoning capabilities")
    print()

def check_ollama_available():
    """Check if Ollama is available"""
    try:
        import subprocess
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        return "llama3.2" in result.stdout
    except FileNotFoundError:
        return False

def check_api_keys_available():
    """Check if cloud API keys are available"""
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    return bool(anthropic_key and openai_key)

def get_user_choice():
    """Get user's choice interactively"""
    print_agent_options()
    
    # Show availability status
    ollama_available = check_ollama_available()
    api_keys_available = check_api_keys_available()
    
    print("Availability Status:")
    print(f"🏠 Ollama (Local): {'✅ Available' if ollama_available else '❌ Not available'}")
    print(f"☁️  API Keys (Cloud): {'✅ Available' if api_keys_available else '❌ Not available'}")
    print()
    
    while True:
        try:
            choice = input("Enter your choice (1-3) or 'q' to quit: ").strip()
            
            if choice.lower() == 'q':
                print("👋 Goodbye!")
                return None
            
            if choice == "1":
                if not ollama_available:
                    print("❌ Ollama not available. Please install Ollama and pull llama3.2 model.")
                    continue
                return "local"
            elif choice == "2":
                if not ollama_available:
                    print("❌ Ollama not available. Please install Ollama and pull llama3.2 model.")
                    continue
                return "advanced"
            elif choice == "3":
                if not api_keys_available:
                    print("❌ API keys not available. Please set ANTHROPIC_API_KEY and OPENAI_API_KEY.")
                    continue
                return "cloud"
            else:
                print("Invalid choice. Please enter 1, 2, 3, or 'q'.")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return None

def run_local_agent():
    """Run the simple local agent"""
    print("🏠 Starting Simple Local Agent...")
    try:
        from simple_ollama_agent import main
        main()
    except ImportError:
        print("❌ Simple Ollama agent not found. Please check ollama_agent.py exists.")
    except Exception as e:
        print(f"❌ Error running local agent: {e}")

def run_advanced_agent():
    """Run the advanced local agent"""
    print("🚀 Starting Advanced Local Agent...")
    try:
        from advanced_ollama_agent import main
        main()
    except ImportError:
        print("❌ Advanced Ollama agent not found. Please check advanced_ollama_agent.py exists.")
    except Exception as e:
        print(f"❌ Error running advanced agent: {e}")

def run_cloud_agent():
    """Run the cloud agent"""
    print("☁️  Starting Cloud Agent...")
    try:
        from cloud_agent import main
        main()
    except ImportError:
        print("❌ Cloud agent not found. Please check cloud_agent.py exists.")
    except Exception as e:
        print(f"❌ Error running cloud agent: {e}")

def run_demo(agent_type: str):
    """Run demo for specified agent type"""
    print(f"🧪 Running {agent_type} agent demo...")
    
    try:
        if agent_type == "local":
            from simple_ollama_agent import demo_questions
            demo_questions()
        elif agent_type == "advanced":
            from advanced_ollama_agent import demo
            demo()
        elif agent_type == "cloud":
            from cloud_agent import demo
            demo()
        else:
            print(f"❌ Unknown agent type: {agent_type}")
    except ImportError as e:
        print(f"❌ Could not import {agent_type} agent: {e}")
    except Exception as e:
        print(f"❌ Error running {agent_type} demo: {e}")

def main():
    """Main launcher function"""
    print_banner()
    
    # Parse command line arguments
    if len(sys.argv) == 1:
        # Interactive mode
        choice = get_user_choice()
        if choice is None:
            return
        
        if choice == "local":
            run_local_agent()
        elif choice == "advanced":
            run_advanced_agent()
        elif choice == "cloud":
            run_cloud_agent()
    
    elif len(sys.argv) == 2:
        # Direct agent selection
        agent_type = sys.argv[1].lower()
        
        if agent_type == "local":
            run_local_agent()
        elif agent_type == "advanced":
            run_advanced_agent()
        elif agent_type == "cloud":
            run_cloud_agent()
        else:
            print(f"❌ Unknown agent type: {agent_type}")
            print("Available types: local, advanced, cloud")
    
    elif len(sys.argv) == 3 and sys.argv[1].lower() == "demo":
        # Demo mode
        agent_type = sys.argv[2].lower()
        run_demo(agent_type)
    
    else:
        print("Usage:")
        print("  python agent_launcher.py                    # Interactive menu")
        print("  python agent_launcher.py local              # Run local agent")
        print("  python agent_launcher.py advanced           # Run advanced local agent")
        print("  python agent_launcher.py cloud              # Run cloud agent")
        print("  python agent_launcher.py demo local         # Run local demo")
        print("  python agent_launcher.py demo advanced      # Run advanced demo")
        print("  python agent_launcher.py demo cloud         # Run cloud demo")

if __name__ == "__main__":
    main()
