#!/usr/bin/env python3
"""
Cloud-based Agno Level 2 Agent (Claude + OpenAI Embeddings)
===========================================================

This is the cloud version of the Level 2 Agno agent based on your provided code.
It uses:
- Claude Sonnet 4 model (requires Anthropic API key)
- OpenAI embeddings (requires OpenAI API key)
- URL-based knowledge loading
- Hybrid search capabilities
- Advanced session management

This is useful when you want the highest quality responses and have API keys available.
For local/free inference, use advanced_ollama_agent.py instead.

Requirements:
- Anthropic API key (for Claude)
- OpenAI API key (for embeddings)
- agno package with cloud dependencies
"""

from agno.agent import Agent
from agno.embedder.openai import OpenAIEmbedder
from agno.knowledge.url import UrlKnowledge
from agno.models.anthropic import Claude
from agno.storage.sqlite import SqliteStorage
from agno.vectordb.lancedb import LanceDb, SearchType
import os
import sys
from typing import Optional, List

class CloudAgent:
    """Cloud-based Agno Agent with Claude and OpenAI embeddings"""
    
    def __init__(
        self,
        model_name: str = "claude-sonnet-4-20250514",
        knowledge_urls: Optional[List[str]] = None,
        use_hybrid_search: bool = True,
        embedder_model: str = "text-embedding-3-small",
        embedder_dimensions: int = 1536,
        storage_db: str = "tmp/cloud_agent.db",
        vector_db_uri: str = "tmp/cloud_lancedb",
    ):
        """
        Initialize the cloud agent
        
        Args:
            model_name: Claude model to use
            knowledge_urls: List of URLs to load as knowledge base
            use_hybrid_search: Whether to use hybrid search (vector + text)
            embedder_model: OpenAI embedding model
            embedder_dimensions: Embedding dimensions
            storage_db: SQLite database file for session storage
            vector_db_uri: LanceDB URI for vector storage
        """
        self.model_name = model_name
        self.knowledge_urls = knowledge_urls or [
            "https://docs.agno.com/introduction.md"
        ]
        self.use_hybrid_search = use_hybrid_search
        self.embedder_model = embedder_model
        self.embedder_dimensions = embedder_dimensions
        self.storage_db = storage_db
        self.vector_db_uri = vector_db_uri
        self.agent = None
        
    def _check_api_keys(self):
        """Check if required API keys are available"""
        anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        openai_key = os.getenv("OPENAI_API_KEY")
        
        missing_keys = []
        if not anthropic_key:
            missing_keys.append("ANTHROPIC_API_KEY")
        if not openai_key:
            missing_keys.append("OPENAI_API_KEY")
        
        if missing_keys:
            print("❌ Missing required API keys:")
            for key in missing_keys:
                print(f"   - {key}")
            print("\nPlease set these environment variables:")
            for key in missing_keys:
                print(f"   export {key}=your_api_key_here")
            return False
        
        return True
        
    def _create_knowledge_base(self):
        """Create knowledge base from URLs with OpenAI embeddings"""
        
        print(f"📚 Creating knowledge base from {len(self.knowledge_urls)} URL(s)...")
        
        # Create OpenAI embedder
        embedder = OpenAIEmbedder(
            id=self.embedder_model,
            dimensions=self.embedder_dimensions
        )
        
        # Create vector database with hybrid search if enabled
        search_type = SearchType.hybrid if self.use_hybrid_search else SearchType.vector
        
        vector_db = LanceDb(
            uri=self.vector_db_uri,
            table_name="cloud_agno_docs",
            search_type=search_type,
            embedder=embedder,
        )
        
        # Create knowledge base from URLs
        knowledge = UrlKnowledge(
            urls=self.knowledge_urls,
            vector_db=vector_db,
        )
        
        return knowledge
    
    def _create_storage(self):
        """Create SQLite storage for session management"""
        return SqliteStorage(
            table_name="cloud_agent_sessions", 
            db_file=self.storage_db
        )
    
    def _create_agent(self):
        """Create the Agno agent with all components"""
        
        # Create knowledge base
        knowledge = self._create_knowledge_base()
        
        # Create storage
        storage = self._create_storage()
        
        # Create agent (based on your provided code)
        agent = Agent(
            name="Agno Cloud Assistant",
            model=Claude(id=self.model_name),
            instructions=[
                "Search your knowledge before answering the question.",
                "Only include the output in your response. No other text.",
            ],
            knowledge=knowledge,
            storage=storage,
            add_datetime_to_instructions=True,
            # Add the chat history to the messages
            add_history_to_messages=True,
            # Number of history runs
            num_history_runs=3,
            markdown=True,
        )
        
        return agent
    
    def initialize(self, recreate_knowledge: bool = False):
        """Initialize the agent and load knowledge base"""
        
        print("🚀 Initializing Cloud Agno Agent with Claude...")
        print("=" * 60)
        
        # Check API keys
        if not self._check_api_keys():
            return False
        
        # Create tmp directory if needed
        os.makedirs("tmp", exist_ok=True)
        
        # Create the agent
        print("🤖 Creating agent...")
        self.agent = self._create_agent()
        
        # Load knowledge base
        print("📚 Loading knowledge base...")
        try:
            self.agent.knowledge.load(recreate=recreate_knowledge)
            print("✅ Knowledge base loaded successfully!")
            return True
        except Exception as e:
            print(f"⚠️  Warning: Could not load knowledge base: {e}")
            print("The agent will still work without the knowledge base.")
            return True
    
    def chat(self):
        """Start interactive chat session"""
        
        if not self.agent:
            print("❌ Agent not initialized. Call initialize() first.")
            return
        
        print("\n🤖 Cloud Agent ready! You can now chat with Claude Sonnet 4.")
        print("Features: Advanced reasoning, knowledge base search, session memory")
        print("Type 'quit', 'exit', or 'bye' to stop the conversation.")
        print("=" * 60)
        
        # Interactive chat loop
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                    print("\n👋 Goodbye! Thanks for using the Cloud agent.")
                    break
                
                if not user_input:
                    continue
                
                print("\n🤖 Assistant:")
                print("-" * 40)
                
                # Get response from agent
                self.agent.print_response(user_input, stream=True)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using the Cloud agent.")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                print("Please try again or type 'quit' to exit.")
    
    def ask(self, question: str, stream: bool = True):
        """Ask a single question to the agent"""
        
        if not self.agent:
            print("❌ Agent not initialized. Call initialize() first.")
            return
        
        print(f"\n📝 Question: {question}")
        print("-" * 50)
        self.agent.print_response(question, stream=stream)


def main():
    """Main function to run the cloud agent"""
    
    # Configuration - you can modify these
    config = {
        "model_name": "claude-sonnet-4-20250514",
        "knowledge_urls": [
            "https://docs.agno.com/introduction.md",
            # You can also use the full documentation:
            # "https://docs.agno.com/llms-full.txt"
        ],
        "use_hybrid_search": True,
        "embedder_model": "text-embedding-3-small",
        "embedder_dimensions": 1536,
    }
    
    # Create agent
    agent = CloudAgent(**config)
    
    # Initialize (set recreate_knowledge=True to refresh knowledge base)
    if agent.initialize(recreate_knowledge=False):
        # Start chat
        agent.chat()


def demo():
    """Run demo questions to test the agent (based on your provided code)"""
    
    print("🧪 Running Cloud Agent Demo...")
    
    # Create agent with demo configuration
    agent = CloudAgent(
        model_name="claude-sonnet-4-20250514",
        knowledge_urls=["https://docs.agno.com/introduction.md"],
        use_hybrid_search=True,
    )
    
    # Initialize
    if not agent.initialize(recreate_knowledge=False):
        return
    
    # Demo question from your provided code
    agent.ask("What is Agno?", stream=True)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo()
    else:
        main()
