# Agno AI Agents Collection

This repository contains two powerful AI agents built with the Agno framework and Ollama for local AI inference.

## 🤖 Projects

### 1. 🔧 API Agent - Natural Language to API Translation
**Location:** `api_agent/`

An intelligent agent that understands natural language queries and automatically translates them into API calls for service desk systems.

**Features:**
- 🗣️ Natural language understanding
- 🔄 Automatic API endpoint detection
- 📝 Smart payload generation
- 🔐 Authentication handling
- 🌐 HTTP server interface for Postman testing

**Example:**
```
Input: "Show me high priority open requests"
Output: Calls search API with correct filters and returns formatted results
```

### 2. 🦙 Ollama Agent Collection - Local AI Assistants
**Location:** `ollama_agent/`

A collection of Level 2 Agno agents with different capabilities, from simple local agents to cloud-powered assistants.

**Agent Types:**
- 🏠 **Simple Local Agent** - Basic Level 2 agent with local knowledge
- 🚀 **Advanced Local Agent** - URL knowledge loading, hybrid search
- ☁️ **Cloud Agent** - Claude Sonnet 4 with OpenAI embeddings
- 🎯 **Agent Launcher** - Easy switching between agent types

**Features:**
- 🏠 100% local execution options (no API keys needed)
- ☁️ Cloud options for highest quality (API keys required)
- 🧠 Knowledge base with vector/hybrid search
- 💾 Persistent conversation memory
- 📚 URL-based knowledge loading
- 💬 Interactive chat interface
- 🔄 Easy model switching

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Ollama installed with Llama 3.2 model
- Virtual environment activated

### Setup
```bash
# Clone or navigate to the project directory
cd /path/to/project

# Activate virtual environment
source .venv/bin/activate

# Choose your agent and follow its README
```

## 📁 Project Structure

```
.
├── README.md                          # This file
├── api_agent/                         # API Agent project
│   ├── src/                          # Source code
│   │   ├── api_agent.py              # Main agent logic
│   │   └── api_agent_server.py       # HTTP server wrapper
│   ├── tests/                        # Test files
│   │   └── test_api_agent.py         # Agent tests
│   ├── docs/                         # Documentation
│   │   ├── README.md                 # API Agent documentation
│   │   └── Postman_Guide.md          # Postman testing guide
│   ├── config/                       # Configuration files
│   │   └── requirements.txt          # Python dependencies
│   └── examples/                     # Example files
│       └── API_Agent_Postman_Collection.json
├── ollama_agent/                     # Ollama Agent Collection
│   ├── src/                          # Source code
│   │   ├── simple_ollama_agent.py    # Simple local agent
│   │   ├── advanced_ollama_agent.py  # Advanced local agent
│   │   ├── cloud_agent.py            # Cloud agent (Claude + OpenAI)
│   │   └── agent_launcher.py         # Agent selection launcher
│   ├── examples/                     # Example implementations
│   │   └── level2_example.py         # Your original Level 2 code
│   ├── docs/                         # Documentation
│   │   ├── README.md                 # Main documentation
│   │   └── LEVEL2_SETUP_GUIDE.md     # Detailed setup guide
│   └── config/                       # Configuration files
│       ├── requirements.txt          # Python dependencies
│       └── setup_ollama_agent.sh     # Setup script
└── tmp/                              # Runtime data (databases, etc.)
```

## 🎯 Which Agent to Use?

### Use API Agent if you want to:
- Convert natural language to API calls
- Integrate with existing service desk systems
- Test APIs through natural language interface
- Build intelligent API wrappers

### Use Ollama Agents if you want to:
- **Simple Local**: Learn Agno basics with minimal setup
- **Advanced Local**: Free agent with URL knowledge and hybrid search
- **Cloud Agent**: Highest quality responses with Claude Sonnet 4
- **Your Original Code**: Exact implementation of your Level 2 pattern
- Privacy-focused AI solution (local agents)
- No ongoing costs (local agents)

## 🛠️ Development

Each agent is self-contained with its own:
- Source code in `src/`
- Documentation in `docs/`
- Configuration in `config/`
- Tests in `tests/`
- Examples in `examples/`

## 📚 Documentation

- **API Agent**: See `api_agent/docs/README.md`
- **Ollama Agent**: See `ollama_agent/docs/README.md`
- **Postman Testing**: See `api_agent/docs/Postman_Guide.md`

## 🤝 Contributing

1. Choose the agent you want to work on
2. Follow the specific README in that agent's directory
3. Make changes in the appropriate `src/` directory
4. Update documentation in `docs/`
5. Add tests in `tests/`

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

For issues or questions:
1. Check the specific agent's README
2. Review the documentation in `docs/`
3. Test with provided examples
4. Create an issue with detailed information

---

**Happy coding with Agno AI Agents!** 🚀
